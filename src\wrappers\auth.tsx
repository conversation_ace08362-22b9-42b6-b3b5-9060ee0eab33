/*
 * @description: 统一页面鉴权
 * @author: zp<PERSON>, AI Assistant
 * @Date: 2021-08-19 11:59:39
 * @LastEditTime: 2023-07-10 10:00:00
 */
import NotFind from '@/pages/403';
import { Access, Outlet, useAccess } from '@umijs/max';
import AuthHandler from '@/components/AuthHandler';
import { isAuthenticated } from '@/utils/auth';
import { getQueryObj } from '@/utils/calc';

export default () => {
  const { isLogin } = useAccess();
  const { code } = getQueryObj();

  // 如果URL中有code参数或者已经认证，使用AuthHandler处理认证
  if (code || isAuthenticated()) {
    return (
      <AuthHandler>
        <Access accessible={isLogin} fallback={<NotFind />}>
          <Outlet />
        </Access>
      </AuthHandler>
    );
  }

  // 如果没有认证，显示未授权页面
  return <NotFind />;
};
