# 认证流程调整说明

## 概述

根据您提供的认证流程要求，我已经对现有的认证系统进行了全面的重构。新的认证流程完全符合您的要求：
- 没有专门的登录页面
- 通过wrapper和403页面处理认证
- 使用code参数进行快速登录
- 认证失败时停留在原页面显示403界面

## 主要更改

### 1. 重构认证函数 (`src/utils/auth.ts`)

#### 核心认证函数：

1. **`login(code: string)`** - 登录函数
   - 用于wrapper和403页面的快速认证
   - 使用授权码进行登录
   - 自动保存令牌和用户信息到localStorage
   - 不进行页面跳转，由调用方决定后续操作

2. **`fetchData(url: string, options?: RequestInit)`** - 带令牌的数据获取函数
   - 自动添加Authorization头部
   - 自动处理响应中的新令牌
   - 支持自定义请求选项

3. **`refreshToken()`** - 刷新令牌函数
   - 使用刷新令牌获取新的访问令牌
   - 刷新失败时自动清除本地存储
   - 返回布尔值表示刷新是否成功

4. **`getCurrentUser()`** - 获取当前用户信息函数
   - 检查访问令牌是否存在
   - 调用后端API获取用户信息
   - 支持新令牌自动更新

5. **`logout()`** - 退出登录函数
   - 调用后端退出API
   - 清除所有本地存储的认证信息
   - 不进行页面跳转

#### 保留的工具函数：

- `getAccessToken()`, `getRefreshToken()`, `getUserInfo()` - 获取本地存储信息
- `clearAuthData()` - 清除认证信息
- `isAuthenticated()` - 检查认证状态
- `refreshAccessToken()` - 兼容性刷新函数（保持现有代码正常工作）
- `handleSilentAuth()` - 兼容性函数（内部调用login函数）

### 2. 修复服务层问题 (`src/services/sso.ts`)

- 修复了 `/auth//refresh-token` 路径中的双斜杠问题
- 确保所有API路径正确

### 3. 完善响应拦截器 (`src/app.tsx`)

- 在响应拦截器中正确处理 `New-Token` 头部
- 自动更新本地存储中的访问令牌
- 移除对登录页面的跳转逻辑

### 4. 统一令牌存储管理

- 统一使用标准的localStorage键名：`accessToken`, `refreshToken`, `userInfo`
- 移除了localStorage_prefix的依赖
- 简化了存储逻辑

## 使用方式

### 基本用法

```typescript
import { login, handleSilentAuth, fetchData, refreshToken, getCurrentUser, logout } from '@/utils/auth';

// 1. 标准登录（有授权码时）
const loginResult = await login('your-authorization-code');
if (loginResult.success) {
  console.log('登录成功');
  // 不会自动跳转，由调用方决定后续操作
}

// 2. 快速认证（用于wrapper和403页面）
const authResult = await handleSilentAuth('your-authorization-code');
if (authResult.success) {
  console.log('认证成功，可以继续访问页面');
} else {
  console.log('认证失败，停留在403页面');
}

// 3. 获取数据
const data = await fetchData('/api/data');

// 4. 刷新令牌
const refreshSuccess = await refreshToken();

// 5. 获取用户信息
const userInfo = await getCurrentUser();

// 6. 退出登录
await logout(); // 只清除本地存储，不跳转页面
```

### 在React组件中使用

```typescript
const MyComponent = () => {
  const handleLogin = async () => {
    const result = await login(code);
    if (result.success) {
      // 登录成功处理，不会自动跳转
      setUser(result.data.userInfo);
    } else {
      alert('登录失败：' + result.error);
    }
  };

  const loadData = async () => {
    try {
      const data = await fetchData('/api/data');
      // 处理数据
    } catch (error) {
      console.error('数据加载失败:', error);
    }
  };

  const handleLogout = async () => {
    await logout();
    setUser(null);
    // 用户需要重新认证才能访问受保护的页面
  };

  // ... 其他逻辑
};
```

## 自动令牌更新机制

系统现在支持多种方式的自动令牌更新：

1. **响应拦截器自动更新**：当API响应包含 `New-Token` 头部时，自动更新本地存储
2. **fetchData函数自动更新**：使用 `fetchData` 函数时自动检查和更新令牌
3. **401错误自动刷新**：现有的401错误处理机制继续工作

## 错误处理

- 所有函数都包含完善的错误处理
- 刷新令牌失败时自动清除认证信息，不进行页面跳转
- 认证失败时停留在原页面，显示403界面
- 提供详细的错误信息用于调试

## 关键变化

### 无登录页面设计
- 移除了所有对登录页面的跳转逻辑
- 认证失败时用户停留在当前页面
- 通过wrapper和403页面处理认证流程

### 简化的存储管理
- 直接使用localStorage，不依赖前缀
- 统一的键名：`accessToken`, `refreshToken`, `userInfo`
- 简化了存储和获取逻辑

## 示例文件

创建了 `src/examples/auth-usage.ts` 文件，包含：
- 各个函数的使用示例
- 完整的认证流程示例
- React组件中的使用示例

## 测试建议

建议创建以下测试用例：

1. **登录流程测试**
   - 成功登录
   - 登录失败处理
   - 令牌保存验证

2. **数据获取测试**
   - 正常数据获取
   - 令牌自动更新
   - 401错误处理

3. **令牌刷新测试**
   - 成功刷新
   - 刷新失败处理

4. **用户信息获取测试**
   - 成功获取
   - 无令牌时的错误处理

5. **退出登录测试**
   - 成功退出
   - 本地存储清除验证
   - 页面跳转验证

## 后续优化建议

1. 添加令牌过期时间检查
2. 实现令牌自动刷新的队列机制
3. 添加更详细的日志记录
4. 考虑添加令牌加密存储
