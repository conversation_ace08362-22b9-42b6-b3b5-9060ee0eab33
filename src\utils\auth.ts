/*
 * @description: 认证相关工具函数
 * @author: AI Assistant
 * @Date: 2023-07-10 10:00:00
 */
import { login, logout as logoutService, refreshToken } from '@/services/sso';

/**
 * 保存认证信息到本地存储
 * @param data 认证数据
 */
export const saveAuthData = (data: any) => {
  if (!data) return;

  // 保存令牌
  localStorage.setItem(localStorage_prefix + 'accessToken', data.accessToken);
  localStorage.setItem(localStorage_prefix + 'refreshToken', data.refreshToken);

  // 保存用户信息
  if (data.userInfo) {
    localStorage.setItem(
      localStorage_prefix + 'userInfo',
      JSON.stringify(data.userInfo),
    );
  }
};

/**
 * 获取访问令牌
 * @returns 访问令牌
 */
export const getAccessToken = (): string => {
  return localStorage.getItem(localStorage_prefix + 'accessToken') || '';
};

/**
 * 获取刷新令牌
 * @returns 刷新令牌
 */
export const getRefreshToken = (): string => {
  return localStorage.getItem(localStorage_prefix + 'refreshToken') || '';
};

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = (): any => {
  const userInfoStr = localStorage.getItem(localStorage_prefix + 'userInfo');
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      return null;
    }
  }
  return null;
};

/**
 * 清除认证信息
 */
export const clearAuthData = () => {
  localStorage.removeItem(localStorage_prefix + 'accessToken');
  localStorage.removeItem(localStorage_prefix + 'refreshToken');
  localStorage.removeItem(localStorage_prefix + 'userInfo');
};

/**
 * 处理快速认证流程
 * @param code URL中的code参数
 * @returns 认证结果
 */
export const handleSilentAuth = async (code: string) => {
  try {
    const loginResult = await login({ code });
    if (loginResult.errCode !== 0) {
      throw new Error(loginResult.msg || '登录失败');
    }

    // 保存认证信息
    saveAuthData(loginResult.data);

    return {
      success: true,
      data: loginResult.data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '认证失败',
    };
  }
};

/**
 * 刷新访问令牌
 * @returns 刷新结果
 */
export const refreshAccessToken = async () => {
  try {
    const refreshTokenValue = getRefreshToken();
    if (!refreshTokenValue) {
      throw new Error('刷新令牌不存在');
    }

    const result = await refreshToken({ refreshToken: refreshTokenValue });
    if (result.errCode !== 0) {
      throw new Error(result.msg || '刷新令牌失败');
    }

    // 更新令牌
    saveAuthData(result.data);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    // 刷新失败，清除认证信息并重定向到登录页
    clearAuthData();
    return {
      success: false,
      error: error instanceof Error ? error.message : '刷新令牌失败',
    };
  }
};

/**
 * 退出登录
 */
export const logout = async () => {
  try {
    await logoutService();
  } finally {
    // 清除本地存储的令牌和用户信息
    clearAuthData();
  }
};

/**
 * 检查是否已认证
 * @returns 是否已认证
 */
export const isAuthenticated = (): boolean => {
  return !!getAccessToken();
};
