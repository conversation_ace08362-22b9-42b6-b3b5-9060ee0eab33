/*
 * @description: 认证相关工具函数
 * @author: AI Assistant
 * @Date: 2023-07-10 10:00:00
 */
import {
  login as loginService,
  logout as logoutService,
  refreshToken as refreshTokenService,
  getCurrentUser as getCurrentUserService
} from '@/services/sso';

/**
 * 获取访问令牌
 * @returns 访问令牌
 */
export const getAccessToken = (): string => {
  return localStorage.getItem('accessToken') || '';
};

/**
 * 获取刷新令牌
 * @returns 刷新令牌
 */
export const getRefreshToken = (): string => {
  return localStorage.getItem('refreshToken') || '';
};

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = (): any => {
  const userInfoStr = localStorage.getItem('userInfo');
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      return null;
    }
  }
  return null;
};

/**
 * 清除认证信息
 */
export const clearAuthData = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('userInfo');
};

/**
 * 登录函数
 * @param code 授权码
 * @returns 登录结果
 */
export const login = async (code: string) => {
  try {
    const response = await loginService({ code });

    if (response.errCode === 0) {
      // 保存令牌
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      // 保存用户信息
      localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));

      return {
        success: true,
        data: response.data,
        message: '登录成功'
      };
    } else {
      return {
        success: false,
        error: response.msg || '登录失败'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '登录失败'
    };
  }
};

/**
 * 刷新令牌函数
 * @returns 刷新结果
 */
export const refreshToken = async () => {
  try {
    const refreshTokenValue = localStorage.getItem('refreshToken');
    if (!refreshTokenValue) {
      throw new Error('刷新令牌不存在');
    }

    const response = await refreshTokenService({ refreshToken: refreshTokenValue });

    if (response.errCode === 0) {
      // 更新令牌
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      return true;
    } else {
      // 刷新失败，清除认证信息
      clearAuthData();
      return false;
    }
  } catch (error) {
    // 刷新失败，清除认证信息
    clearAuthData();
    return false;
  }
};

/**
 * 刷新访问令牌（兼容性函数）
 * @returns 刷新结果
 */
export const refreshAccessToken = async () => {
  try {
    const refreshTokenValue = getRefreshToken();
    if (!refreshTokenValue) {
      throw new Error('刷新令牌不存在');
    }

    const result = await refreshTokenService({ refreshToken: refreshTokenValue });
    if (result.errCode !== 0) {
      throw new Error(result.msg || '刷新令牌失败');
    }

    // 更新令牌
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    if (result.data.userInfo) {
      localStorage.setItem('userInfo', JSON.stringify(result.data.userInfo));
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    // 刷新失败，清除认证信息
    clearAuthData();
    return {
      success: false,
      error: error instanceof Error ? error.message : '刷新令牌失败',
    };
  }
};

/**
 * 使用访问令牌获取数据
 * @param url API地址
 * @param options 请求选项
 * @returns 响应数据
 */
export const fetchData = async (url: string, options: RequestInit = {}) => {
  const accessToken = localStorage.getItem('accessToken');

  const response = await fetch(url, {
    method: 'GET',
    ...options,
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      ...options.headers,
    },
  });

  // 检查是否有新令牌
  const newToken = response.headers.get('New-Token');
  if (newToken) {
    localStorage.setItem('accessToken', newToken);
  }

  return await response.json();
};

/**
 * 获取当前用户信息
 * @returns 用户信息
 */
export const getCurrentUser = async () => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('未找到访问令牌');
    }

    const response = await getCurrentUserService();

    // 检查是否有新令牌（如果使用自定义fetch）
    // const newToken = response.headers?.get('New-Token');
    // if (newToken) {
    //   localStorage.setItem('accessToken', newToken);
    // }

    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 退出登录
 */
export const logout = async () => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      await logoutService();
    }
  } finally {
    // 清除本地存储的令牌和用户信息
    clearAuthData();
  }
};

/**
 * 处理快速认证流程（用于wrapper和403页面）
 * @param code URL中的code参数
 * @returns 认证结果
 */
export const handleSilentAuth = async (code: string) => {
  try {
    const loginResult = await loginService({ code });
    if (loginResult.errCode !== 0) {
      throw new Error(loginResult.msg || '登录失败');
    }

    // 保存认证信息
    localStorage.setItem('accessToken', loginResult.data.accessToken);
    localStorage.setItem('refreshToken', loginResult.data.refreshToken);
    if (loginResult.data.userInfo) {
      localStorage.setItem('userInfo', JSON.stringify(loginResult.data.userInfo));
    }

    return {
      success: true,
      data: loginResult.data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '认证失败',
    };
  }
};

/**
 * 检查是否已认证
 * @returns 是否已认证
 */
export const isAuthenticated = (): boolean => {
  return !!getAccessToken();
};
