/*
 * @description: 404路由
 * @author: zpl
 * @Date: 2021-07-14 17:11:16
 * @LastEditTime: 2023-03-08 10:12:17
 * @LastEditors: wuzhan
 */
import React from 'react';
import { history } from '@umijs/max';
import { Result, Button } from 'antd';

const NotFind = () => {
  return (
    <Result
      status="404"
      title="404"
      subTitle={'抱歉，您访问的页面不存在。'}
      extra={
        <Button
          type="primary"
          onClick={() => {
            history.replace('/');
          }}
        >
          返回首页
        </Button>
      }
    />
  );
};

export default NotFind;
