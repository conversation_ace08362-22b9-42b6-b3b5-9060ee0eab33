/*
 * @description: 认证流程使用示例
 * @author: AI Assistant
 * @Date: 2023-07-10 10:00:00
 */

import {
  login,
  refreshToken,
  fetchData,
  getCurrentUser,
  logout,
  handleSilentAuth
} from '@/utils/auth';

/**
 * 登录示例（用于有授权码的情况）
 */
export const loginExample = async () => {
  try {
    const result = await login('your-authorization-code');
    if (result.success) {
      console.log('登录成功:', result.data);
      // 登录成功，令牌已保存到localStorage
    } else {
      console.error('登录失败:', result.error);
      alert('登录失败：' + result.error);
    }
  } catch (error) {
    console.error('登录异常:', error);
  }
};

/**
 * 快速认证示例（用于wrapper和403页面）
 */
export const silentAuthExample = async () => {
  try {
    const result = await handleSilentAuth('your-authorization-code');
    if (result.success) {
      console.log('快速认证成功:', result.data);
      // 认证成功，可以继续访问页面
    } else {
      console.error('快速认证失败:', result.error);
      // 认证失败，停留在403页面
    }
  } catch (error) {
    console.error('快速认证异常:', error);
  }
};

/**
 * 使用访问令牌获取数据示例
 */
export const fetchDataExample = async () => {
  try {
    const data = await fetchData('/api/data');
    console.log('获取数据成功:', data);
    return data;
  } catch (error) {
    console.error('获取数据失败:', error);
    throw error;
  }
};

/**
 * 刷新令牌示例
 */
export const refreshTokenExample = async () => {
  try {
    const success = await refreshToken();
    if (success) {
      console.log('令牌刷新成功');
      return true;
    } else {
      console.log('令牌刷新失败，已跳转到登录页');
      return false;
    }
  } catch (error) {
    console.error('刷新令牌异常:', error);
    return false;
  }
};

/**
 * 获取当前用户信息示例
 */
export const getCurrentUserExample = async () => {
  try {
    const userInfo = await getCurrentUser();
    console.log('用户信息:', userInfo);
    return userInfo;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
};

/**
 * 退出登录示例
 */
export const logoutExample = async () => {
  try {
    await logout();
    console.log('退出登录成功');
    // 会自动清除本地存储，用户需要重新认证
  } catch (error) {
    console.error('退出登录失败:', error);
  }
};

/**
 * 完整的认证流程示例
 */
export const authFlowExample = {
  // 1. 登录
  async login(code: string) {
    const result = await login(code);
    if (result.success) {
      console.log('✅ 登录成功');
      return result.data;
    } else {
      console.error('❌ 登录失败:', result.error);
      throw new Error(result.error);
    }
  },

  // 2. 获取数据
  async getData() {
    try {
      const data = await fetchData('/api/some-data');
      console.log('✅ 数据获取成功');
      return data;
    } catch (error) {
      console.error('❌ 数据获取失败:', error);
      throw error;
    }
  },

  // 3. 刷新令牌
  async refreshAuth() {
    const success = await refreshToken();
    if (success) {
      console.log('✅ 令牌刷新成功');
    } else {
      console.log('❌ 令牌刷新失败，需要重新登录');
    }
    return success;
  },

  // 4. 获取用户信息
  async getUserInfo() {
    try {
      const userInfo = await getCurrentUser();
      console.log('✅ 用户信息获取成功');
      return userInfo;
    } catch (error) {
      console.error('❌ 用户信息获取失败:', error);
      throw error;
    }
  },

  // 5. 退出登录
  async signOut() {
    try {
      await logout();
      console.log('✅ 退出登录成功');
      // 用户需要重新认证才能访问受保护的页面
    } catch (error) {
      console.error('❌ 退出登录失败:', error);
    }
  }
};

/**
 * React组件中的使用示例
 */
export const ReactComponentExample = `
// 在React组件中使用认证功能

import React, { useEffect, useState } from 'react';
import { login, fetchData, getCurrentUser, logout } from '@/utils/auth';

const MyComponent = () => {
  const [user, setUser] = useState(null);
  const [data, setData] = useState(null);

  // 登录处理
  const handleLogin = async () => {
    try {
      const result = await login('your-code');
      if (result.success) {
        // 登录成功，获取用户信息
        const userInfo = await getCurrentUser();
        setUser(userInfo.data);
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  // 获取数据
  const loadData = async () => {
    try {
      const result = await fetchData('/api/data');
      setData(result);
    } catch (error) {
      console.error('数据加载失败:', error);
    }
  };

  // 退出登录
  const handleLogout = async () => {
    try {
      await logout();
      setUser(null);
      setData(null);
      // 用户需要重新认证才能访问受保护的页面
    } catch (error) {
      console.error('退出失败:', error);
    }
  };

  useEffect(() => {
    // 组件加载时检查认证状态
    const checkAuth = async () => {
      try {
        const userInfo = await getCurrentUser();
        setUser(userInfo.data);
      } catch (error) {
        // 用户未认证
        console.log('用户未认证');
      }
    };

    checkAuth();
  }, []);

  return (
    <div>
      {user ? (
        <div>
          <p>欢迎, {user.name}</p>
          <button onClick={loadData}>加载数据</button>
          <button onClick={handleLogout}>退出登录</button>
          {data && <pre>{JSON.stringify(data, null, 2)}</pre>}
        </div>
      ) : (
        <button onClick={handleLogin}>登录</button>
      )}
    </div>
  );
};

export default MyComponent;
`;
