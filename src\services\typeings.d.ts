declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  type LoginResponse = {
    token: string;
    refreshToken: string;
    user: Omit<User, 'password'>;
  };

  type User = {
      accessToken: string;
      refreshToken: string;
      userInfo: {
        id: string;
        userCode: string;
        username: string;
        realName: string;
        avatar?: string;
        enterpriseCode: string;
        enterpriseName: string;
        roles: string[];
        mobile: string;
        email: string;
        enterprise: {
          id: string;
          code: string;
          name: string;
          type_code: string;
          type_name: string;
          region: string;
          region_code: string;
        };
      };
      params?: Record<string, any>;
    };
}
